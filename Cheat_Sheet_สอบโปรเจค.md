# 📋 Cheat Sheet - สอบโปรเจค (ใช้ดูด่วนขณะสอบ)

## 🎯 ข้อมูลพื้นฐาน (ต้องจำ)

### 📊 **ตัวเลขสำคัญ**
- **ตารางฐานข้อมูล:** 11 ตาราง
- **หน้าเว็บ:** 8 หน้าหลัก (5 หน้าบ้าน + 3 Admin)
- **Controllers:** 4 ตัวหลัก
- **Models:** 8 โมเดล
- **เวลาพัฒนา:** 2-3 เดือน

### 🌐 **URL หลัก**
- `/` - หน้าหลัก
- `/services` - บริการ
- `/packages` - แพ็คเกจ
- `/activities` - ผลงาน
- `/contact` - ติดต่อ
- `/admin/login` - Admin

---

## 💻 เทคโนโลยีหลัก (ต้องพูดได้)

### 🏗️ **Backend**
- **Laravel 9.x** - PHP Framework
- **Eloquent ORM** - Database
- **Blade Template** - View Engine
- **MySQL 8.0+** - Database

### 🎨 **Frontend**
- **Bootstrap 5** - CSS Framework
- **jQuery** - JavaScript
- **HTML5/CSS3** - Structure & Style
- **Responsive Design** - Mobile Support

---

## 🗃️ ฐานข้อมูล (11 ตาราง)

### 📋 **ตารางหลัก**
1. **users** (8) - Admin
2. **services** (9) - บริการ
3. **service_images** (6) - รูปบริการ
4. **packages** (11) - แพ็คเกจ
5. **activities** (10) - ผลงาน
6. **activity_images** (6) - รูปผลงาน
7. **banners** (9) - แบนเนอร์
8. **contacts** (7) - ข้อความ
9. **site_settings** (4) - ตั้งค่า
10. **service_categories** (6) - หมวดหมู่
11. **migrations** (3) - ระบบ

### 🔗 **Relationships**
- services → service_images (1:Many)
- activities → activity_images (1:Many)

---

## ❓ คำถาม + คำตอบด่วน

### **"อธิบายโปรเจค"**
"เว็บไซต์ธุรกิจจัดงานศพ พัฒนาด้วย Laravel มีหน้าบ้าน-หลังบ้าน ใช้ MySQL 11 ตาราง Responsive ทุกอุปกรณ์"

### **"ทำไมเลือก Laravel?"**
"เป็นมาตรฐาน มี Security ดี มี ORM ป้องกัน SQL Injection Community ใหญ่"

### **"ฟีเจอร์หลักคืออะไร?"**
"แสดงบริการ แสดงแพ็คเกจ แสดงผลงาน ระบบติดต่อ Admin Panel"

### **"ระบบรักษาความปลอดภัย?"**
"CSRF Protection, SQL Injection Prevention, XSS Protection, Password Hashing"

### **"เปลี่ยนสีเว็บยังไง?"**
"แก้ไข public/css/funeral-style.css ส่วน :root CSS Variables"

### **"เปลี่ยนโลโก้ยังไง?"**
"ใส่รูปใน public/images/ แก้ไข layouts/app.blade.php บรรทัด 130"

---

## 🛠️ ไฟล์สำคัญ (ตำแหน่งที่ต้องรู้)

### 📁 **Controllers**
- `app/Http/Controllers/HomeController.php` - หน้าบ้าน
- `app/Http/Controllers/AdminController.php` - หลังบ้าน
- `app/Http/Controllers/AuthController.php` - ล็อกอิน

### 📁 **Models**
- `app/Models/Service.php` - บริการ
- `app/Models/Package.php` - แพ็คเกจ
- `app/Models/Activity.php` - ผลงาน

### 📁 **Views**
- `resources/views/layouts/app.blade.php` - Template หลัก
- `resources/views/frontend/home.blade.php` - หน้าหลัก

### 📁 **Config**
- `routes/web.php` - URL Routes
- `config/app.php` - App Settings

---

## 🎯 จุดเด่น (ต้องพูดได้)

### ✅ **Strengths**
1. ใช้เทคโนโลยีทันสมัย
2. ระบบรักษาความปลอดภัยดี
3. Responsive Design
4. ฐานข้อมูลออกแบบดี
5. Admin Panel ครบถ้วน
6. SEO Friendly
7. User Experience ดี

### 🔄 **Future Improvements**
1. เพิ่มระบบ Search
2. เพิ่มระบบ Review
3. เพิ่ม API
4. เพิ่มระบบ Payment
5. Multi-language

---

## 🚨 คำถามยาก + คำตอบ

### **"Performance ปัญหาจะแก้ยังไง?"**
"Database Index, Caching (Redis), CDN, Load Balancer, Query Optimization"

### **"Hacker เข้ามาจะป้องกันยังไง?"**
"Input Validation, CSRF Token, SQL Injection Prevention, XSS Protection, HTTPS, Rate Limiting"

### **"เพิ่ม Payment ยังไง?"**
"เพิ่มตาราง payments, ใช้ Omise/Stripe Gateway, เพิ่ม SSL Certificate"

### **"ทำ Mobile App ยังไง?"**
"สร้าง RESTful API, ใช้ Laravel Sanctum, API Documentation, JSON Response"

---

## 📊 Technical Specs

### 💻 **Requirements**
- PHP 8.0+
- MySQL 8.0+
- Apache/Nginx
- Composer
- Node.js & NPM

### 📦 **Key Packages**
- Laravel Framework 9.x
- Bootstrap 5.3.0
- jQuery 3.6.0
- Font Awesome 6.4.0

---

## 🎭 การสาธิต (ลำดับการแสดง)

### 🎬 **Demo Flow**
1. **หน้าหลัก** - อธิบายส่วนต่างๆ
2. **บริการ** - คลิกดูรายละเอียด
3. **แพ็คเกจ** - แสดงราคา
4. **ผลงาน** - ดูแกลเลอรี่
5. **ติดต่อ** - ทดสอบฟอร์ม
6. **Admin Login** - เข้าระบบ
7. **เพิ่มข้อมูล** - สาธิตการเพิ่ม
8. **แก้ไขข้อมูล** - แสดงการแก้ไข

---

## 🔧 Commands ที่อาจใช้

### 🚀 **Laravel Commands**
```bash
php artisan serve          # รันเซิร์ฟเวอร์
php artisan migrate         # รัน migration
php artisan cache:clear     # ล้าง cache
php artisan config:clear    # ล้าง config
php artisan storage:link    # สร้าง storage link
```

### 📦 **NPM Commands**
```bash
npm run dev                 # Compile assets
npm run watch              # Watch changes
npm run production         # Production build
```

---

## 🆘 แก้ปัญหาด่วน

### 🖼️ **รูปไม่แสดง**
1. `php artisan storage:link`
2. ตรวจสอบ path ใน `<img src="">`
3. ตรวจสอบไฟล์อยู่ใน `public/storage/`

### 🎨 **CSS ไม่เปลี่ยน**
1. `npm run dev`
2. `php artisan cache:clear`
3. Hard refresh (Ctrl+F5)

### 🔐 **Login ไม่ได้**
1. ตรวจสอบ email/password ในตาราง users
2. รีเซ็ตรหัสผ่านด้วย SQL
3. ตรวจสอบ session config

---

## 📋 Checklist ก่อนสอบ

### ✅ **เทคนิค**
- [ ] เว็บไซต์ทำงานปกติ
- [ ] Database มีข้อมูลครบ
- [ ] Admin Panel เข้าได้
- [ ] ทุกหน้าโหลดได้
- [ ] รูปภาพแสดงครับ

### ✅ **เนื้อหา**
- [ ] จำข้อมูลพื้นฐานได้
- [ ] อธิบายเทคโนโลยีได้
- [ ] ตอบคำถามทั่วไปได้
- [ ] แสดงการแก้ไขได้
- [ ] บอกจุดเด่นได้

---

## 💡 เคล็ดลับสุดท้าย

### 🎯 **ระหว่างสอบ**
- พูดช้าๆ ชัดเจน
- แสดงหน้าจอขณะอธิบาย
- มั่นใจในผลงาน
- ยอมรับข้อจำกัด
- ถามคำถามกลับ

### 🚀 **หากไม่รู้คำตอบ**
- "เรื่องนี้ผมยังไม่ได้ศึกษาลึก"
- "คิดว่าน่าจะทำได้โดย..."
- "ต้องไปตรวจสอบเพิ่มเติม"

### 🌟 **แสดงความกระตือรือร้น**
- "เป็นไอเดียที่ดีมาก"
- "ผมจะเอาไปพัฒนาต่อ"
- "ขอบคุณสำหรับข้อเสนอแนะ"

---

**🔥 จำไว้: คุณทำได้ดีมาก แค่มั่นใจและอธิบายสิ่งที่รู้ จะผ่านแน่นอน! 💪**
